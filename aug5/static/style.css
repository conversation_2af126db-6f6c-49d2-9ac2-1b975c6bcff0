* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 1200px;
    height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.header h1 {
    font-size: 2rem;
    margin-bottom: 5px;
}

.header p {
    opacity: 0.9;
    font-size: 1rem;
}

.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.upload-section {
    background: #f8f9fa;
    padding: 20px;
    border-right: 1px solid #e9ecef;
    width: 300px;
    display: flex;
    flex-direction: column;
}

.upload-area {
    border: 2px dashed #667eea;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    background: white;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #764ba2;
    background: #f8f9fa;
}

.upload-area.dragover {
    border-color: #28a745;
    background: #d4edda;
}

.upload-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 10px;
}

.upload-text {
    color: #6c757d;
    margin-bottom: 15px;
}

.file-input {
    display: none;
}

.upload-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.upload-status {
    margin-top: 15px;
    padding: 10px;
    border-radius: 5px;
    text-align: center;
    font-weight: bold;
}

.upload-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.upload-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.chat-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
}

.message {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
}

.message.user {
    justify-content: flex-end;
}

.message-content {
    max-width: 70%;
    padding: 15px;
    border-radius: 15px;
    word-wrap: break-word;
}

.message.user .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom-right-radius: 5px;
}

.message.bot .message-content {
    background: white;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.suggested-questions {
    margin-top: 15px;
}

.suggested-questions h4 {
    color: #667eea;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.suggestion-btn {
    display: block;
    width: 100%;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 5px;
    text-align: left;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.suggestion-btn:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.chat-input-area {
    padding: 20px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.input-group {
    display: flex;
    gap: 10px;
}

.chat-input {
    flex: 1;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 25px;
    font-size: 1rem;
    outline: none;
    transition: all 0.3s ease;
}

.chat-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.send-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.send-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.send-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.new-chat-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    margin-top: 10px;
    transition: all 0.3s ease;
}

.new-chat-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }

    .upload-section {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #e9ecef;
    }

    .container {
        height: 95vh;
        margin: 10px;
    }

    .message-content {
        max-width: 85%;
    }
}

/* Scrollbar styling */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #764ba2;
}
