class BRSRChatbot {
    constructor() {
        this.apiBaseUrl = 'http://localhost:8001';
        this.isUploading = false;
        this.isSending = false;
        this.initializeElements();
        this.attachEventListeners();
        this.checkServerStatus();
    }

    initializeElements() {
        this.uploadArea = document.getElementById('uploadArea');
        this.fileInput = document.getElementById('fileInput');
        this.uploadBtn = document.getElementById('uploadBtn');
        this.uploadStatus = document.getElementById('uploadStatus');
        this.chatMessages = document.getElementById('chatMessages');
        this.chatInput = document.getElementById('chatInput');
        this.sendBtn = document.getElementById('sendBtn');
        this.newChatBtn = document.getElementById('newChatBtn');
    }

    attachEventListeners() {
        // File upload events
        this.uploadArea.addEventListener('click', () => this.fileInput.click());
        this.uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        this.uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        this.uploadArea.addEventListener('drop', this.handleDrop.bind(this));
        this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));
        this.uploadBtn.addEventListener('click', this.uploadFile.bind(this));

        // Chat events
        this.sendBtn.addEventListener('click', this.sendMessage.bind(this));
        this.chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        this.newChatBtn.addEventListener('click', this.newChat.bind(this));
    }

    async checkServerStatus() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/ping`);
            if (response.ok) {
                this.showStatus('Server is running and ready!', 'success');
            } else {
                this.showStatus('Server is not responding properly', 'error');
            }
        } catch (error) {
            this.showStatus('Cannot connect to server. Please ensure the Flask app is running.', 'error');
        }
    }

    handleDragOver(e) {
        e.preventDefault();
        this.uploadArea.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.fileInput.files = files;
            this.handleFileSelect();
        }
    }

    handleFileSelect() {
        const file = this.fileInput.files[0];
        if (file) {
            if (file.type === 'application/pdf') {
                this.uploadBtn.textContent = `Upload ${file.name}`;
                this.uploadBtn.disabled = false;
                this.showStatus('PDF file selected. Click upload to process.', 'success');
            } else {
                this.showStatus('Please select a PDF file only.', 'error');
                this.uploadBtn.disabled = true;
            }
        }
    }

    async uploadFile() {
        if (this.isUploading || !this.fileInput.files[0]) return;

        this.isUploading = true;
        this.uploadBtn.disabled = true;
        this.uploadBtn.innerHTML = '<div class="loading"></div> Uploading...';

        const formData = new FormData();
        formData.append('file', this.fileInput.files[0]);

        try {
            const response = await fetch(`${this.apiBaseUrl}/upload`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (response.ok) {
                this.showStatus('PDF uploaded and processed successfully! You can now ask questions.', 'success');
                this.chatInput.disabled = false;
                this.sendBtn.disabled = false;
            } else {
                this.showStatus(`Upload failed: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showStatus(`Upload error: ${error.message}`, 'error');
        } finally {
            this.isUploading = false;
            this.uploadBtn.disabled = false;
            this.uploadBtn.textContent = 'Upload PDF';
        }
    }

    async sendMessage() {
        const message = this.chatInput.value.trim();
        if (!message || this.isSending) return;

        this.isSending = true;
        this.sendBtn.disabled = true;
        this.sendBtn.innerHTML = '<div class="loading"></div>';

        // Add user message to chat
        this.addMessage(message, 'user');
        this.chatInput.value = '';

        try {
            const response = await fetch(`${this.apiBaseUrl}/ask`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ question: message })
            });

            const result = await response.json();

            if (response.ok) {
                this.addMessage(result.answer, 'bot', result.suggested_questions);
            } else {
                this.addMessage(`Error: ${result.error}`, 'bot');
            }
        } catch (error) {
            this.addMessage(`Error: ${error.message}`, 'bot');
        } finally {
            this.isSending = false;
            this.sendBtn.disabled = false;
            this.sendBtn.innerHTML = 'Send';
        }
    }

    addMessage(content, sender, suggestedQuestions = []) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.textContent = content;

        messageDiv.appendChild(messageContent);

        // Add suggested questions for bot messages
        if (sender === 'bot' && suggestedQuestions && suggestedQuestions.length > 0) {
            const suggestionsDiv = document.createElement('div');
            suggestionsDiv.className = 'suggested-questions';
            
            const title = document.createElement('h4');
            title.textContent = 'Suggested follow-up questions:';
            suggestionsDiv.appendChild(title);

            suggestedQuestions.forEach(question => {
                const btn = document.createElement('button');
                btn.className = 'suggestion-btn';
                btn.textContent = question;
                btn.addEventListener('click', () => {
                    this.chatInput.value = question;
                    this.sendMessage();
                });
                suggestionsDiv.appendChild(btn);
            });

            messageContent.appendChild(suggestionsDiv);
        }

        this.chatMessages.appendChild(messageDiv);
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    async newChat() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/new-chat`, {
                method: 'POST'
            });

            if (response.ok) {
                this.chatMessages.innerHTML = '';
                this.addMessage('Chat session reset. You can start a new conversation!', 'bot');
            }
        } catch (error) {
            this.addMessage(`Error resetting chat: ${error.message}`, 'bot');
        }
    }

    showStatus(message, type) {
        this.uploadStatus.textContent = message;
        this.uploadStatus.className = `upload-status ${type}`;
        this.uploadStatus.style.display = 'block';
    }
}

// Initialize the chatbot when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new BRSRChatbot();
});
