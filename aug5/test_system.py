#!/usr/bin/env python3
"""
Test script for BRSR Chatbot System
This script tests the basic functionality of the chatbot API
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:8001"

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing health check endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/ping")
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"   Response: {response.json()}")
            return True
        else:
            print(f"❌ Health check failed with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check failed with error: {e}")
        return False

def test_frontend():
    """Test if the frontend is accessible"""
    print("\n🔍 Testing frontend accessibility...")
    try:
        response = requests.get(BASE_URL)
        if response.status_code == 200:
            print("✅ Frontend is accessible")
            return True
        else:
            print(f"❌ Frontend failed with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend test failed with error: {e}")
        return False

def test_ask_without_document():
    """Test asking a question without uploading a document"""
    print("\n🔍 Testing ask endpoint without document...")
    try:
        data = {"question": "What is BRSR?"}
        response = requests.post(
            f"{BASE_URL}/ask",
            headers={"Content-Type": "application/json"},
            json=data
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Ask endpoint responded")
            print(f"   Answer: {result.get('answer', 'No answer')[:100]}...")
            return True
        else:
            print(f"❌ Ask endpoint failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Ask endpoint test failed with error: {e}")
        return False

def test_new_chat():
    """Test the new chat endpoint"""
    print("\n🔍 Testing new chat endpoint...")
    try:
        response = requests.post(f"{BASE_URL}/new-chat")
        if response.status_code == 200:
            print("✅ New chat endpoint works")
            print(f"   Response: {response.json()}")
            return True
        else:
            print(f"❌ New chat failed with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ New chat test failed with error: {e}")
        return False

def main():
    """Run all tests"""
    print("🤖 BRSR Chatbot System Test")
    print("=" * 40)
    
    tests = [
        test_health_check,
        test_frontend,
        test_ask_without_document,
        test_new_chat
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        time.sleep(1)  # Small delay between tests
    
    print("\n" + "=" * 40)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The system is working correctly.")
        print("\n📝 Next steps:")
        print("1. Upload a BRSR PDF document through the web interface")
        print("2. Ask questions about BRSR requirements")
        print("3. Use suggested questions for better exploration")
    else:
        print("⚠️ Some tests failed. Please check the system configuration.")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure the Flask app is running on port 8001")
        print("2. Check if all dependencies are installed")
        print("3. Verify ChromaDB is working properly")

if __name__ == "__main__":
    main()
