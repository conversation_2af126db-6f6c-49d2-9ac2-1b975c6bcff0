# Ollama API Configuration
OLLAMA_API_URL=http://localhost:11434

# ChromaDB Configuration
CHROMA_DB_PATH=./chroma_store

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_HOST=0.0.0.0
FLASK_PORT=8000

# External LLM API Configuration (if using hosted service)
LLM_API_URL=https://llm.aivot.ai/generate
LLM_API_TOKEN=ollamahostapi1234

# Logging Configuration
LOG_LEVEL=INFO

# File Upload Configuration
MAX_CONTENT_LENGTH=16777216  # 16MB in bytes
UPLOAD_FOLDER=./uploads

# Security Configuration (for production)
SECRET_KEY=your-secret-key-here

# CORS Configuration
CORS_ORIGINS=*

# Model Configuration
EMBEDDING_MODEL=nomic-embed-text
GENERATION_MODEL=llama3.2:latest
CHUNK_SIZE=512
TOP_K_RESULTS=5
