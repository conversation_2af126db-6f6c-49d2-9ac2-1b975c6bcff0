#!/bin/bash

# BRSR Chatbot Setup Script
# This script sets up the complete BRSR chatbot system

set -e  # Exit on any error

echo "🤖 Setting up AisupIntel BRSR Chatbot..."
echo "========================================"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8+ first."
    exit 1
fi

# Check Python version
python_version=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
echo "✅ Python version: $python_version"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install requirements
echo "📥 Installing Python dependencies..."
pip install -r requirements.txt

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p chroma_store
mkdir -p uploads
mkdir -p logs

# Copy environment file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "⚙️ Creating environment configuration..."
    cp .env.template .env
    echo "✏️ Please edit .env file with your configuration"
fi

# Check if Ollama is installed
if ! command -v ollama &> /dev/null; then
    echo "⚠️ Ollama is not installed."
    echo "Please install Ollama from https://ollama.ai"
    echo "Then run: ollama pull nomic-embed-text && ollama pull llama3.2:latest"
else
    echo "✅ Ollama is installed"
    
    # Check if required models are available
    if ollama list | grep -q "nomic-embed-text"; then
        echo "✅ nomic-embed-text model is available"
    else
        echo "📥 Pulling nomic-embed-text model..."
        ollama pull nomic-embed-text
    fi
    
    if ollama list | grep -q "llama3.2"; then
        echo "✅ llama3.2 model is available"
    else
        echo "📥 Pulling llama3.2 model..."
        ollama pull llama3.2:latest
    fi
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "To start the application:"
echo "1. Activate virtual environment: source venv/bin/activate"
echo "2. Start Ollama (if not running): ollama serve"
echo "3. Run the application: python chatBotRagNomicOllama.py"
echo "4. Open http://localhost:8000 in your browser"
echo ""
echo "For Docker deployment:"
echo "1. docker-compose up -d"
echo "2. docker exec -it aug5_ollama_1 ollama pull nomic-embed-text"
echo "3. docker exec -it aug5_ollama_1 ollama pull llama3.2:latest"
echo ""
echo "📖 See README.md for detailed instructions"
