version: '3.8'

services:
  brsr-chatbot:
    build: .
    ports:
      - "8000:8000"
    environment:
      - FLASK_ENV=production
      - OLLAMA_API_URL=http://ollama:11434
      - CHROMA_DB_PATH=/app/chroma_store
      - LLM_API_URL=https://llm.aivot.ai/generate
      - LLM_API_TOKEN=ollamahostapi1234
    volumes:
      - ./chroma_store:/app/chroma_store
      - ./uploads:/app/uploads
    depends_on:
      - ollama
    restart: unless-stopped
    networks:
      - brsr-network

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    restart: unless-stopped
    networks:
      - brsr-network
    # Uncomment the following lines if you have GPU support
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

volumes:
  ollama_data:

networks:
  brsr-network:
    driver: bridge
