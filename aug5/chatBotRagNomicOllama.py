# rag_brsr_ollama_flask.py (Flask-based RAG setup with persistent storage and chat session history)

import os
import shutil
import pdfplumber
import logging
import json
import re
import subprocess
from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import chromadb
# from chromadb.config import Settings
from chromadb import PersistentClient
import requests
from dotenv import load_dotenv

# Load environment variables from .env
load_dotenv()

# Environment setup
OLLAMA_API_URL = os.getenv("OLLAMA_API_URL", "http://localhost:11434")
CHROMA_DB_PATH = os.getenv("CHROMA_DB_PATH", "./chroma_store")

# ----------------------------
# Configure logging
# ----------------------------
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("RAG_BRSR")

# ----------------------------
# Initialize Flask App, Persistent ChromaDB & Session Store
# ----------------------------
app = Flask(__name__)
CORS(app)

# chroma_client = chromadb.Client(Settings(
#     chroma_db_impl="duckdb+parquet",
#     persist_directory="./chroma_store"
# ))


# chroma_client = PersistentClient(path="./chroma_store")
chroma_client = PersistentClient(path=CHROMA_DB_PATH)

collection = chroma_client.get_or_create_collection(name="brsr_data")
session_history = []

# ----------------------------
# Prompt Template
# ----------------------------
prompt_template = """
Your are "AisupIntel", developed by "AIVOT AI Pvt. ltd", an intelligent assistant specialized in BRSR (Business Responsibility and Sustainability Reporting) and an AI-powered platforms, the AI Sustainability Platform (AISUP). Your responses should be based solely on the context provided below, which covers the requirements and structure of the BRSR framework and details about AISUP.
Answer the following question using only the information provided in the context.

Context:
{context}

Question:
{question}

If the question is unrelated to BRSR or if the context does not contain relevant data to answer it, politely respond with:
"Sorry, I can only answer questions related to BRSR based on the provided information. Please ask a question related to Business Responsibility and Sustainability Reporting."
"""

# ----------------------------
# Step 1: Extract Text from PDF
# ----------------------------
def extract_text_from_pdf(pdf_path):
    logger.info("Extracting text from PDF...")
    with pdfplumber.open(pdf_path) as pdf:
        text = "\n".join(
            page.extract_text() for page in pdf.pages if page.extract_text()
        )
    logger.info("PDF text extraction complete.")
    return text

# ----------------------------
# Step 2: Chunking the text
# ----------------------------
def chunk_text(text, max_length=512):
    logger.info("Chunking text...")
    paragraphs = text.split('\n')
    chunks, current_chunk = [], ""
    for para in paragraphs:
        if len(current_chunk) + len(para) < max_length:
            current_chunk += para + " "
        else:
            chunks.append(current_chunk.strip())
            current_chunk = para + " "
    if current_chunk:
        chunks.append(current_chunk.strip())
    logger.info(f"Created {len(chunks)} chunks.")
    return chunks

# ----------------------------
# Step 3: Generate Embeddings using Ollama
# ----------------------------

def get_nomic_embeddings(text_chunks):
    logger.info("Generating embeddings using local 'nomic-embed-text' model via Ollama API...")

    embeddings = []
    for i, chunk in enumerate(text_chunks):
        try:
            logger.debug(f"Generating embedding for chunk {i + 1}/{len(text_chunks)}...")
            response = requests.post(
                f"{OLLAMA_API_URL}/api/embeddings",
                json={
                    'model': 'nomic-embed-text',
                    'prompt': chunk
                }
            )

            if response.status_code != 200:
                logger.error(f"Ollama API error for chunk {i}: {response.text}")
                raise RuntimeError(f"Failed to generate embedding for chunk {i}")

            data = response.json()
            if 'embedding' not in data:
                logger.error(f"No 'embedding' in response for chunk {i}: {data}")
                raise ValueError(f"Invalid embedding response for chunk {i}")

            embeddings.append(data['embedding'])
        except Exception as e:
            logger.exception(f"Error processing chunk {i}: {str(e)}")
            raise

    logger.info(f"Successfully generated embeddings for {len(embeddings)} chunks.")
    return embeddings

# ----------------------------
# Step 4: Store Chunks in ChromaDB
# ----------------------------
def store_chunks_in_chroma(chunks, embeddings):
    logger.info("Storing embeddings in ChromaDB...")
    for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
        collection.add(
            documents=[chunk],
            embeddings=[embedding],
            ids=[f"chunk_{i}"]
        )
    # print(type(chroma_client))
    # chroma_client.persist()
    logger.info("Chunks stored and persisted in ChromaDB.")

# ----------------------------
# Step 5: Retrieve Relevant Context
# ----------------------------
def retrieve_context_from_chroma(query, top_k=5):
    logger.info("Retrieving relevant context from ChromaDB using requests...")

    try:
        response = requests.post(
            f"{OLLAMA_API_URL}/api/embeddings",
            json={
                'model': 'nomic-embed-text',
                'prompt': query
            }
        )

        if response.status_code != 200:
            logger.error(f"Ollama embedding API error: {response.text}")
            raise RuntimeError("Failed to generate query embedding.")

        data = response.json()
        query_embedding = data.get('embedding')

        if not query_embedding:
            logger.error("No embedding returned in response.")
            raise ValueError("Invalid embedding response from Ollama.")
    except Exception as e:
        logger.exception("Error during embedding generation.")
        raise e

    results = collection.query(query_embeddings=[query_embedding], n_results=top_k)

    context = " ".join(doc for docs in results['documents'] for doc in docs)
    logger.info("Context retrieval complete.")
    return context

# ----------------------------
# Step 6: Generate Answer using Ollama Llama3 3B
# ----------------------------
def generate_with_ollama(prompt):
    logger.info("Calling Ollama model via HTTP...")

    try:
        response = requests.post(
            # f"{OLLAMA_API_URL}/api/generate",
            f"https://llm.aivot.ai/generate",
            headers={
                'Authorization': 'Bearer ollamahostapi1234',
            },
            json={
                'model': 'llama3.2:latest',  # Or the exact name you've pulled (e.g., llama3)
                # 'model': 'mistral:7b',  # Or the exact name you've pulled (e.g., llama3)
                'prompt': prompt,
                'stream': False
            }
        )
        if response.status_code != 200:
            logger.error(f"Ollama generation API error: {response.text}")
            return "Error generating response."
        data = response.json()
        answer = data.get('response', '').strip()

        logger.info("Ollama response generated successfully.")
        return answer if answer else "No response generated."
    
    except Exception as e:
        logger.exception("Error calling Ollama generate API.")
        return "Error generating response."

# ----------------------------
# Step 7: Generate Suggestions using Ollama Llama3 3B
# ----------------------------

def get_suggested_questions(model_response):
    follow_up_prompt = (
        f"Based on this response: '{model_response}', suggest exactly three follow-up questions "
        "a user might ask. Respond strictly in the following JSON format:\n"
        '{ "suggested_questions": ["Question 1", "Question 2", "Question 3"] }\n'
        "Do not include any additional text or explanations, only the JSON object."
    )

    follow_up_response = generate_with_ollama(follow_up_prompt)

    # suggested_questions = []
    # try:
    #     parsed_json = json.loads(follow_up_response)
    #     suggested_questions = parsed_json.get("suggested_questions", [])
    # except json.JSONDecodeError as e:
    #     print(f"JSON parsing error: {e}")
    #     suggested_questions = []

    follow_up_text = follow_up_response if isinstance(follow_up_response, str) else follow_up_response.text
    suggested_questions = extract_suggested_questions(follow_up_text)

    return suggested_questions

def extract_suggested_questions(response_text):
    logger.info(f"start extracting questions from: {response_text}")
    try:
        # Extract the JSON part
        match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if match:
            json_text = match.group(0)
            parsed = json.loads(json_text)
            return parsed.get("suggested_questions", [])
        else:
            print("No JSON found in model response.")
            return []
    except Exception as e:
        print(f"JSON parsing error: {e}")
        return []

# ----------------------------
# Frontend Route
# ----------------------------
@app.route('/')
def index():
    return render_template('index.html')

# ----------------------------
# Upload Endpoint
# ----------------------------
@app.route('/ping', methods=['GET'])
def health_check():
    return {'status': 'API is running'}, 200

@app.route("/upload", methods=["POST"])
def upload_pdf():
    logger.info("Received PDF upload request.")
    if 'file' not in request.files:
        return jsonify({"error": "No file part in request."}), 400

    file = request.files['file']
    temp_pdf_path = "temp_uploaded.pdf"
    file.save(temp_pdf_path)

    try:
        text = extract_text_from_pdf(temp_pdf_path)
        chunks = chunk_text(text)
        embeddings = get_nomic_embeddings(chunks)

        # collection.delete(where={})

        logger.info("Fetching and deleting existing documents from ChromaDB...")
        existing = collection.get()
        if "ids" in existing and existing["ids"]:
            collection.delete(ids=existing["ids"])
            logger.info(f"Deleted {len(existing['ids'])} documents from ChromaDB.")
        else:
            logger.info("No existing documents found to delete.")
        
        store_chunks_in_chroma(chunks, embeddings)

        return jsonify({"message": "PDF uploaded and processed successfully."})
    except Exception as e:
        logger.exception("Error while processing PDF upload.")
        return jsonify({"error": str(e)}), 500
    finally:
        os.remove(temp_pdf_path)
        logger.info("Cleaned up temp files.")

# ----------------------------
# Question Answering Endpoint
# ----------------------------
@app.route("/ask", methods=["POST"])
def ask_brsr():
    # question = request.form.get("question")
    data = request.get_json()
    question = data.get("question")
    logger.info("Received question request.")

    if not question:
        return jsonify({"error": "Missing question parameter."}), 400

    try:
        context = retrieve_context_from_chroma(question)
        final_prompt = prompt_template.format(context=context, question=question)
        answer = generate_with_ollama(final_prompt)

        session_history.append({"question": question, "answer": answer})

        # suggested_questions = [
        #     f"Can you elaborate more on: {question}?",
        #     "What additional data supports this response?",
        #     "What are the implications of this answer for sustainability reporting?"
        # ]

        suggested_questions = get_suggested_questions(answer)

        return jsonify({
            "answer": answer,
            "chat_history": session_history,
            "suggested_questions": suggested_questions
        })
    except Exception as e:
        logger.exception("Error while answering question.")
        return jsonify({"error": str(e)}), 500

# ----------------------------
# Reset Chat Session Endpoint
# ----------------------------
@app.route("/new-chat", methods=["POST"])
def new_chat():
    logger.info("Resetting chat session history.")
    session_history.clear()
    return jsonify({"message": "Chat session reset."})

# ----------------------------
# Run Flask app
# ----------------------------
if __name__ == "__main__":
    app.run(debug=True, host="0.0.0.0", port=8001)
