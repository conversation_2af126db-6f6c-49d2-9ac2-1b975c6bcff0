# 🤖 AisupIntel - BRSR Chatbot

**AisupIntel** is an AI-powered chatbot specialized in Business Responsibility and Sustainability Reporting (BRSR) developed by AIVOT AI Pvt. Ltd. This system uses RAG (Retrieval-Augmented Generation) technology to provide intelligent responses based on uploaded BRSR documents.

## 🌟 Features

- **PDF Document Processing**: Upload and process BRSR PDF documents
- **Intelligent Q&A**: Ask questions about BRSR requirements and get contextual answers
- **Vector Search**: Uses ChromaDB for efficient document retrieval
- **Modern Web Interface**: Clean, responsive web UI for easy interaction
- **Suggested Questions**: AI-generated follow-up questions for better exploration
- **Chat History**: Maintains conversation context within sessions
- **Docker Support**: Easy deployment with Docker and Docker Compose

## 🏗️ Architecture

- **Backend**: Flask web framework with RESTful APIs
- **AI Models**: Ollama with nomic-embed-text for embeddings and llama3.2 for generation
- **Vector Database**: ChromaDB for persistent vector storage
- **Frontend**: Vanilla JavaScript with modern CSS
- **Document Processing**: PDFPlumber for text extraction

## 📋 Prerequisites

### Option 1: Local Installation
- Python 3.8+
- Ollama installed and running
- Required models: `nomic-embed-text` and `llama3.2:latest`

### Option 2: Docker Installation
- Docker and Docker Compose
- 4GB+ RAM recommended
- GPU support optional but recommended

## 🚀 Quick Start

### Using Docker (Recommended)

1. **Clone and navigate to the project**:
   ```bash
   git clone <repository-url>
   cd aug5
   ```

2. **Start the services**:
   ```bash
   docker-compose up -d
   ```

3. **Pull required Ollama models**:
   ```bash
   docker exec -it aug5_ollama_1 ollama pull nomic-embed-text
   docker exec -it aug5_ollama_1 ollama pull llama3.2:latest
   ```

4. **Access the application**:
   Open http://localhost:8000 in your browser

### Local Installation

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up environment**:
   ```bash
   cp .env.template .env
   # Edit .env with your configuration
   ```

3. **Install and start Ollama**:
   ```bash
   # Install Ollama (visit https://ollama.ai for instructions)
   ollama pull nomic-embed-text
   ollama pull llama3.2:latest
   ```

4. **Run the application**:
   ```bash
   python chatBotRagNomicOllama.py
   ```

5. **Access the application**:
   Open http://localhost:8000 in your browser

## 📖 Usage Guide

### 1. Upload a BRSR Document
- Click the upload area or drag and drop a PDF file
- Wait for the processing to complete
- The system will extract text, create embeddings, and store them in ChromaDB

### 2. Ask Questions
- Type your question in the chat input
- Questions should be related to BRSR or sustainability reporting
- Use suggested questions for guidance

### 3. Explore with Follow-ups
- Click on suggested questions to dive deeper
- Start a new chat session to reset context

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OLLAMA_API_URL` | Ollama API endpoint | `http://localhost:11434` |
| `CHROMA_DB_PATH` | ChromaDB storage path | `./chroma_store` |
| `FLASK_HOST` | Flask host | `0.0.0.0` |
| `FLASK_PORT` | Flask port | `8000` |
| `LLM_API_URL` | External LLM API URL | `https://llm.aivot.ai/generate` |
| `LLM_API_TOKEN` | API token for external LLM | `ollamahostapi1234` |

### Model Configuration

- **Embedding Model**: `nomic-embed-text` (384-dimensional embeddings)
- **Generation Model**: `llama3.2:latest` (or configurable)
- **Chunk Size**: 512 characters
- **Top-K Results**: 5 most relevant chunks

## 📡 API Endpoints

### Health Check
```http
GET /ping
```
Returns server status.

### Upload PDF
```http
POST /upload
Content-Type: multipart/form-data

file: <PDF file>
```

### Ask Question
```http
POST /ask
Content-Type: application/json

{
  "question": "What are the BRSR requirements?"
}
```

### New Chat Session
```http
POST /new-chat
```
Resets the chat history.

## 🐳 Docker Commands

```bash
# Start services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild and start
docker-compose up --build

# Access Ollama container
docker exec -it aug5_ollama_1 bash
```

## 🔍 Troubleshooting

### Common Issues

1. **Ollama not responding**:
   - Ensure Ollama is running: `ollama serve`
   - Check if models are pulled: `ollama list`

2. **ChromaDB errors**:
   - Delete `chroma_store` directory and restart
   - Check disk space and permissions

3. **PDF processing fails**:
   - Ensure PDF is not password-protected
   - Check file size (max 16MB by default)

4. **Docker issues**:
   - Increase Docker memory allocation
   - Check port conflicts (8000, 11434)

### Logs and Debugging

- Application logs: Check console output
- Docker logs: `docker-compose logs brsr-chatbot`
- Ollama logs: `docker-compose logs ollama`

## 🛠️ Development

### Project Structure
```
aug5/
├── chatBotRagNomicOllama.py    # Main Flask application
├── requirements.txt             # Python dependencies
├── .env                        # Environment configuration
├── Dockerfile                  # Docker configuration
├── docker-compose.yml          # Multi-container setup
├── static/                     # Frontend assets
│   ├── style.css              # Styling
│   └── script.js              # JavaScript logic
├── templates/                  # HTML templates
│   └── index.html             # Main interface
└── chroma_store/              # Vector database (auto-created)
```

### Adding Features

1. **New API endpoints**: Add routes in `chatBotRagNomicOllama.py`
2. **Frontend changes**: Modify files in `static/` and `templates/`
3. **Model configuration**: Update environment variables
4. **Dependencies**: Add to `requirements.txt`

## 📄 License

This project is developed by AIVOT AI Pvt. Ltd. All rights reserved.

## 🤝 Support

For support and questions, please contact AIVOT AI Pvt. Ltd.

---

**Note**: This system is designed specifically for BRSR-related queries. For best results, upload comprehensive BRSR documentation and ask specific questions about sustainability reporting requirements.

## 🚦 System Requirements

### Minimum Requirements
- **RAM**: 4GB (8GB recommended)
- **Storage**: 2GB free space
- **CPU**: 2 cores (4 cores recommended)
- **Network**: Internet connection for model downloads

### Recommended for Production
- **RAM**: 16GB+
- **Storage**: 10GB+ SSD
- **CPU**: 8+ cores
- **GPU**: NVIDIA GPU with 8GB+ VRAM (optional but significantly improves performance)

## 🔐 Security Considerations

- Change default secret keys in production
- Use HTTPS in production environments
- Implement proper authentication if needed
- Regularly update dependencies
- Monitor file upload sizes and types
- Consider rate limiting for API endpoints

## 🎯 Performance Tips

1. **Use GPU acceleration** with Ollama for faster inference
2. **Optimize chunk size** based on your document types
3. **Adjust top-k results** for better relevance vs speed trade-off
4. **Use SSD storage** for ChromaDB for faster retrieval
5. **Scale horizontally** with multiple instances behind a load balancer
