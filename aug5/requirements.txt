# Core Flask dependencies
Flask==2.3.3
Flask-CORS==4.0.0

# PDF processing
pdfplumber==0.10.0

# Vector database
chromadb==0.4.15

# HTTP requests
requests==2.31.0

# Environment variables
python-dotenv==1.0.0

# Logging (built-in, but explicit for clarity)
# logging - built-in Python module

# JSON handling (built-in)
# json - built-in Python module

# Regular expressions (built-in)
# re - built-in Python module

# File operations (built-in)
# os - built-in Python module
# shutil - built-in Python module

# Process management (built-in)
# subprocess - built-in Python module

# Additional useful dependencies for production
gunicorn==21.2.0
waitress==2.1.2

# Development dependencies
pytest==7.4.2
pytest-flask==1.2.0
black==23.9.1
flake8==6.1.0

# Optional: For better error handling and monitoring
sentry-sdk[flask]==1.32.0
