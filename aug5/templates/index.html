<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AisupIntel - BRSR Chatbot</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🤖</text></svg>">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AisupIntel</h1>
            <p>Your AI-powered BRSR (Business Responsibility and Sustainability Reporting) Assistant</p>
            <p><small>Developed by AIVOT AI Pvt. Ltd.</small></p>
        </div>
        
        <div class="main-content">
            <div class="upload-section">
                <h3>📄 Upload BRSR Document</h3>
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">
                        <strong>Click to upload</strong> or drag and drop<br>
                        PDF files only
                    </div>
                    <input type="file" id="fileInput" class="file-input" accept=".pdf">
                </div>
                <button id="uploadBtn" class="upload-btn" disabled>Upload PDF</button>
                <div id="uploadStatus" class="upload-status" style="display: none;"></div>
                
                <button id="newChatBtn" class="new-chat-btn">🔄 New Chat Session</button>
                
                <div style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 8px; font-size: 0.85rem;">
                    <h4 style="color: #1976d2; margin-bottom: 8px;">💡 How to use:</h4>
                    <ol style="margin-left: 15px; color: #424242;">
                        <li>Upload a BRSR PDF document</li>
                        <li>Wait for processing to complete</li>
                        <li>Ask questions about BRSR requirements</li>
                        <li>Use suggested questions for guidance</li>
                    </ol>
                </div>
            </div>
            
            <div class="chat-section">
                <div class="chat-messages" id="chatMessages">
                    <div class="message bot">
                        <div class="message-content">
                            <p>👋 Hello! I'm AisupIntel, your BRSR assistant.</p>
                            <p>Please upload a BRSR PDF document to get started. Once uploaded, I can help you with:</p>
                            <ul style="margin: 10px 0; padding-left: 20px;">
                                <li>Understanding BRSR requirements</li>
                                <li>Explaining sustainability reporting frameworks</li>
                                <li>Answering questions about business responsibility</li>
                                <li>Providing guidance on compliance</li>
                            </ul>
                            <p><em>Note: I can only answer questions based on the uploaded BRSR document content.</em></p>
                        </div>
                    </div>
                </div>
                
                <div class="chat-input-area">
                    <div class="input-group">
                        <input 
                            type="text" 
                            id="chatInput" 
                            class="chat-input" 
                            placeholder="Ask a question about BRSR..." 
                            disabled
                        >
                        <button id="sendBtn" class="send-btn" disabled>Send</button>
                    </div>
                    <div style="margin-top: 10px; font-size: 0.8rem; color: #666; text-align: center;">
                        Press Enter to send • Upload a PDF first to enable chat
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
